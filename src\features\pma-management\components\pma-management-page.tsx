'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { usePmaManagementTranslations } from '@/hooks/use-translations';
import {
    Download,
    Edit2,
    Eye,
    Filter,
    Plus,
    RefreshCw,
    Search
} from 'lucide-react';
import { useState } from 'react';

// Mock data based on the image provided
const mockPmaData = [
  {
    pmaNumber: 'PMA-2024-001',
    customer: 'DBKL',
    project: 'KL Tower Maintenance',
    contractor: 'ABC Construction',
    createdDate: '2024-01-15',
    expiryDate: '2024-12-15',
    status: 'Active',
  },
  {
    pmaNumber: 'PMA-2024-002',
    customer: 'JKR Selangor',
    project: 'Highway Repair',
    contractor: 'DEF Engineering',
    createdDate: '2024-02-01',
    expiryDate: '2024-11-30',
    status: 'Expiring Soon',
  },
  {
    pmaNumber: 'PMA-2024-003',
    customer: 'Tenaga Nasional',
    project: 'Power Grid Upgrade',
    contractor: 'GHI Power Systems',
    createdDate: '2024-03-10',
    expiryDate: '2025-03-10',
    status: 'Active',
  },
  {
    pmaNumber: 'PMA-2023-045',
    customer: 'Telekom Malaysia',
    project: 'Fiber Network',
    contractor: 'JKL Networks',
    createdDate: '2023-08-20',
    expiryDate: '2024-08-20',
    status: 'Expired',
  },
  {
    pmaNumber: 'PMA-2024-004',
    customer: 'SPAN',
    project: 'Water Treatment',
    contractor: 'MNO Water Tech',
    createdDate: '2024-04-05',
    expiryDate: '2025-04-05',
    status: 'Active',
  },
];

const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case 'Active':
      return 'default';
    case 'Expiring Soon':
      return 'secondary';
    case 'Expired':
      return 'destructive';
    default:
      return 'outline';
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Active':
      return 'text-green-600';
    case 'Expiring Soon':
      return 'text-orange-600';
    case 'Expired':
      return 'text-red-600';
    default:
      return 'text-gray-600';
  }
};

export function PmaManagementPage() {
  const t = usePmaManagementTranslations();
  const [searchQuery, setSearchQuery] = useState('');

  // Calculate statistics
  const stats = {
    total: mockPmaData.length,
    active: mockPmaData.filter((pma) => pma.status === 'Active').length,
    expiringSoon: mockPmaData.filter((pma) => pma.status === 'Expiring Soon')
      .length,
    expired: mockPmaData.filter((pma) => pma.status === 'Expired').length,
  };

  // Filter data based on search query
  const filteredData = mockPmaData.filter(
    (pma) =>
      pma.pmaNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pma.customer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pma.project.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pma.contractor.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="space-y-6 p-6">      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            {t('title')}
          </h1>
          <p className="text-muted-foreground">
            {t('subtitle')}
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          {t('newLogEntry')}
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('statistics.activePmas')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.active}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('statistics.completed')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">8</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('statistics.expiringSoon')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.expiringSoon}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('statistics.overdue')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.expired}</div>
          </CardContent>
        </Card>
      </div>

      {/* PMA Activity Logs */}
      <Card>        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t('activityLogs.title')}</CardTitle>
              <p className="text-sm text-muted-foreground">
                {t('activityLogs.subtitle')}
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                {t('filter')}
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                {t('export')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search Bar */}
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />              <Input
                placeholder={t('searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>              <TableHeader>
                <TableRow>
                  <TableHead>{t('table.pmaNumber')}</TableHead>
                  <TableHead>{t('table.customer')}</TableHead>
                  <TableHead>{t('table.project')}</TableHead>
                  <TableHead>{t('table.contractor')}</TableHead>
                  <TableHead>{t('table.createdDate')}</TableHead>
                  <TableHead>{t('table.expiryDate')}</TableHead>
                  <TableHead>{t('table.status')}</TableHead>
                  <TableHead>{t('table.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((pma) => (
                  <TableRow key={pma.pmaNumber}>
                    <TableCell className="font-medium text-blue-600">
                      {pma.pmaNumber}
                    </TableCell>
                    <TableCell>{pma.customer}</TableCell>
                    <TableCell>{pma.project}</TableCell>
                    <TableCell>{pma.contractor}</TableCell>
                    <TableCell>{pma.createdDate}</TableCell>
                    <TableCell>{pma.expiryDate}</TableCell>                    <TableCell>
                      <Badge
                        variant={getStatusBadgeVariant(pma.status)}
                        className={getStatusColor(pma.status)}
                      >
                        {pma.status === 'Active' && t('status.active')}
                        {pma.status === 'Expiring Soon' && t('status.expiringSoon')}
                        {pma.status === 'Expired' && t('status.expired')}
                        {pma.status === 'Completed' && t('status.completed')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit2 className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>          {/* Pagination */}
          <div className="flex items-center justify-between pt-4">
            <p className="text-sm text-muted-foreground">
              {t('pagination.showing', { count: filteredData.length, total: stats.total })}
            </p>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" disabled>
                {t('pagination.previous')}
              </Button>
              <Button variant="outline" size="sm" className="bg-primary text-primary-foreground">
                1
              </Button>
              <Button variant="outline" size="sm">
                2
              </Button>
              <Button variant="outline" size="sm">
                3
              </Button>
              <Button variant="outline" size="sm">
                {t('pagination.next')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
